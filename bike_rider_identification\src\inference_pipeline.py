from ultralytics import YOLO
import cv2
import torch
import json
import os
import numpy as np
from torchvision import transforms, models
from PIL import Image
from utils import get_dominant_color, extract_clothing_colors, map_to_target_colors
from tracker import Tracker

class BikeRiderInferencePipeline:
    def __init__(self):
        """Initialize the inference pipeline with models and configurations"""
        
        # Load YOLOv8 pretrained COCO model
        print("Loading YOLOv8 model...")
        try:
            self.det_model = YOLO("yolov8n.pt")
        except Exception as e:
            print(f"Warning: YOLO loading issue: {e}")
            # Try alternative loading method
            import torch
            torch.serialization.add_safe_globals([])
            self.det_model = YOLO("yolov8n.pt")
        
        # Load ResNet bike classifier (2-class model: Splendor vs combined Yamaha)
        print("Loading ResNet bike classifier...")
        self.bike_model = models.resnet18(pretrained=False)
        self.bike_model.fc = torch.nn.Linear(self.bike_model.fc.in_features, 2)
        
        # Load the 2-class model weights
        if os.path.exists("models/resnet_bike2.pth"):
            self.bike_model.load_state_dict(torch.load("models/resnet_bike2.pth", map_location="cpu"))
            print("✅ Loaded 2-class bike model")
        else:
            print("❌ 2-class bike model not found!")
        
        self.bike_model.eval()
        
        # Final bike classes (mapping from 2-class model)
        # Class 0: "1" -> "Splendor", Class 1: "Splendor" -> "Splendor"
        # Any 4-class results (CD-70, CG-125, Prider, yamaha) -> "Yamaha"
        self.bike_classes = ["Splendor", "Yamaha"]  # Final output classes
        
        # Image preprocessing for ResNet
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
        ])
        
        # Initialize tracker
        self.tracker = Tracker()
        
        # Storage for profiles
        self.profiles = {}
        
        print("🚀 Inference pipeline initialized!")
    
    def classify_bike(self, bike_crop):
        """Classify bike model using ResNet and map to final categories"""
        if bike_crop is None or bike_crop.size == 0:
            return "Yamaha"  # Default fallback

        try:
            # Convert BGR to RGB
            img = Image.fromarray(cv2.cvtColor(bike_crop, cv2.COLOR_BGR2RGB))
            tensor = self.transform(img).unsqueeze(0)

            with torch.no_grad():
                pred = self.bike_model(tensor)
                class_idx = pred.argmax().item()

            # Map 2-class model output to final categories
            # Based on dataset analysis:
            # - 2-class model: Class 0 = "1", Class 1 = "Splendor"
            # - Final mapping: "Splendor" stays "Splendor", everything else becomes "Yamaha"

            if class_idx == 1:  # Class "Splendor" from 2-class model
                return "Splendor"
            else:  # Class "1" from 2-class model -> map to Yamaha
                return "Yamaha"

        except Exception as e:
            print(f"Error in bike classification: {e}")
            return "Yamaha"  # Default fallback
    
    def process_frame(self, frame):
        """Process a single frame and return detections"""
        results = self.det_model(frame, verbose=False)
        detections = []
        
        for r in results:
            if r.boxes is not None:
                for box in r.boxes:
                    cls = int(box.cls[0])
                    label = self.det_model.names[cls]
                    conf = float(box.conf[0])
                    x1, y1, x2, y2 = map(int, box.xyxy[0])
                    
                    # Filter for motorcycles and persons with good confidence
                    if conf > 0.5:
                        if label == "motorcycle":
                            bike_crop = frame[y1:y2, x1:x2]
                            model_label = self.classify_bike(bike_crop)
                            bike_color = map_to_target_colors(get_dominant_color(bike_crop))
                            
                            detections.append({
                                'bbox': [x1, y1, x2, y2],
                                'type': 'bike',
                                'model': model_label,
                                'color': bike_color,
                                'conf': conf
                            })
                            
                        elif label == "person":
                            person_crop = frame[y1:y2, x1:x2]
                            clothing_colors = extract_clothing_colors(person_crop)
                            
                            detections.append({
                                'bbox': [x1, y1, x2, y2],
                                'type': 'person',
                                'clothing': clothing_colors,
                                'conf': conf
                            })
        
        return detections
    
    def associate_persons_with_bikes(self, detections):
        """Associate detected persons with nearby bikes"""
        bikes = [d for d in detections if d['type'] == 'bike']
        persons = [d for d in detections if d['type'] == 'person']
        
        associations = []
        
        for bike in bikes:
            bike_x1, bike_y1, bike_x2, bike_y2 = bike['bbox']
            bike_center_x = (bike_x1 + bike_x2) / 2
            bike_center_y = (bike_y1 + bike_y2) / 2
            
            # Find persons near this bike
            nearby_persons = []
            for person in persons:
                person_x1, person_y1, person_x2, person_y2 = person['bbox']
                person_center_x = (person_x1 + person_x2) / 2
                person_center_y = (person_y1 + person_y2) / 2
                
                # Calculate distance
                distance = np.sqrt((bike_center_x - person_center_x)**2 + 
                                 (bike_center_y - person_center_y)**2)
                
                # If person is within reasonable distance of bike
                if distance < 200:  # Adjust threshold as needed
                    nearby_persons.append(person)
            
            # Sort by distance and take closest as rider, others as pillion
            if nearby_persons:
                distances = []
                for person in nearby_persons:
                    px1, py1, px2, py2 = person['bbox']
                    pcx, pcy = (px1 + px2) / 2, (py1 + py2) / 2
                    dist = np.sqrt((bike_center_x - pcx)**2 + (bike_center_y - pcy)**2)
                    distances.append(dist)
                
                # Sort persons by distance to bike
                sorted_persons = [p for _, p in sorted(zip(distances, nearby_persons))]
                
                rider = sorted_persons[0] if sorted_persons else None
                pillion_count = len(sorted_persons) - 1 if len(sorted_persons) > 1 else 0
                
                associations.append({
                    'bike': bike,
                    'rider': rider,
                    'pillion_count': pillion_count
                })
        
        return associations
    
    def run_inference(self, video_path, output_video_path="outputs/demo_out.mp4", 
                     output_json_path="outputs/profiles.json"):
        """Run inference on video and generate outputs"""
        
        # Create output directory
        os.makedirs("outputs", exist_ok=True)
        
        # Open video
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print(f"❌ Error: Could not open video {video_path}")
            return
        
        # Get video properties
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        print(f"📹 Processing video: {width}x{height} @ {fps}fps, {total_frames} frames")
        
        # Setup video writer
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_video_path, fourcc, fps, (width, height))
        
        frame_count = 0
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            if frame_count % 30 == 0:  # Progress update every 30 frames
                print(f"Processing frame {frame_count}/{total_frames}")
            
            # Process frame
            detections = self.process_frame(frame)
            associations = self.associate_persons_with_bikes(detections)
            
            # Update tracker with bike detections
            bike_tracks = []
            for assoc in associations:
                bike = assoc['bike']
                x1, y1, x2, y2 = bike['bbox']
                bike_tracks.append([x1, y1, x2, y2, bike['model'], bike['color']])
            
            # Update tracker
            tracks = self.tracker.update(bike_tracks)
            
            # Update profiles and annotate frame
            for track in tracks:
                x1, y1, x2, y2, track_id, model, color = track
                
                # Find corresponding association
                rider_info = None
                pillion_count = 0
                
                for assoc in associations:
                    bike_bbox = assoc['bike']['bbox']
                    if (abs(bike_bbox[0] - x1) < 10 and abs(bike_bbox[1] - y1) < 10):
                        rider_info = assoc['rider']
                        pillion_count = assoc['pillion_count']
                        break
                
                # Update profile
                biker_id = str(track_id)
                if biker_id not in self.profiles:
                    self.profiles[biker_id] = {
                        "BikerID": biker_id,
                        "Model": model,
                        "BikeColor": color,
                        "Rider": {"ShirtColor": "other", "PantColor": "other"},
                        "PillionCount": 0
                    }
                
                # Update rider clothing if detected
                if rider_info:
                    self.profiles[biker_id]["Rider"] = rider_info['clothing']
                    self.profiles[biker_id]["PillionCount"] = pillion_count
                
                # Annotate frame
                cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                label = f"ID:{biker_id} {model} ({color})"
                cv2.putText(frame, label, (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
            
            # Write frame
            out.write(frame)
        
        # Cleanup
        cap.release()
        out.release()
        
        # Save profiles JSON
        with open(output_json_path, 'w') as f:
            json.dump(list(self.profiles.values()), f, indent=2)
        
        print(f"✅ Processing complete!")
        print(f"📹 Output video: {output_video_path}")
        print(f"📄 Profiles JSON: {output_json_path}")
        print(f"👥 Total bikers detected: {len(self.profiles)}")

def main():
    """Main function to run the inference pipeline"""
    pipeline = BikeRiderInferencePipeline()
    
    # Example usage - replace with your video path
    video_path = "demo_video.mp4"  # Change this to your input video
    
    if os.path.exists(video_path):
        pipeline.run_inference(video_path)
    else:
        print(f"❌ Video file not found: {video_path}")
        print("Please provide a valid video file path")

if __name__ == "__main__":
    main()
