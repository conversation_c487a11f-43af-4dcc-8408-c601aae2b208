import cv2
import json
import torch
import argparse
import os
from ultralytics import YOL<PERSON>
from torchvision import transforms, models
from PIL import Image
from utils import get_dominant_color, extract_clothing_colors
from tracker import Tracker

def load_bike_classifier(model_path, num_classes, device):
    """Load trained ResNet bike classifier"""
    model = models.resnet18()
    model.fc = torch.nn.Linear(model.fc.in_features, num_classes)
    
    if os.path.exists(model_path):
        model.load_state_dict(torch.load(model_path, map_location=device))
        print(f"✅ Loaded bike classifier from {model_path}")
    else:
        print(f"⚠️ Model file {model_path} not found. Using untrained model.")
    
    model.eval()
    return model

def classify_bike(crop, model, classes, transform, device):
    """Classify bike model from crop"""
    if crop is None or crop.size == 0:
        return "unknown"
    
    try:
        # Convert BGR to RGB
        img = Image.fromarray(cv2.cvtColor(crop, cv2.COLOR_BGR2RGB))
        tensor = transform(img).unsqueeze(0).to(device)
        
        with torch.no_grad():
            pred = model(tensor)
            class_idx = pred.argmax().item()
            confidence = torch.softmax(pred, dim=1)[0][class_idx].item()
        
        # Return class name if confidence is high enough
        if confidence > 0.5:
            return classes[class_idx]
        else:
            return "unknown"
    except Exception as e:
        print(f"Error in bike classification: {e}")
        return "unknown"

def main():
    parser = argparse.ArgumentParser(description='Bike & Rider Identification System')
    parser.add_argument('--input', type=str, default='demo_video.mp4', help='Input video path')
    parser.add_argument('--output', type=str, default='outputs/demo_out.mp4', help='Output video path')
    parser.add_argument('--model', type=str, choices=['2class', '4class'], default='4class', 
                       help='Bike classification model to use')
    parser.add_argument('--conf', type=float, default=0.5, help='YOLO confidence threshold')
    
    args = parser.parse_args()
    
    # Setup device
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")
    
    # Load YOLO pretrained COCO model
    print("Loading YOLO model...")
    det_model = YOLO("yolov8n.pt")
    
    # Setup bike classification model
    if args.model == '4class':
        classes = ["CD-70", "CG-125", "Prider", "yamaha"]
        model_path = "models/resnet_bike4.pth"
    else:
        classes = ["1", "Splendor"]
        model_path = "models/resnet_bike2.pth"
    
    print(f"Loading bike classifier for {len(classes)} classes...")
    bike_model = load_bike_classifier(model_path, len(classes), device)
    bike_model = bike_model.to(device)
    
    # Image transform for bike classification
    transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
    ])
    
    # Initialize tracker
    tracker = Tracker()
    
    # Open video
    cap = cv2.VideoCapture(args.input)
    if not cap.isOpened():
        print(f"Error: Could not open video {args.input}")
        return
    
    # Get video properties
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    
    # Create output directory
    os.makedirs(os.path.dirname(args.output), exist_ok=True)
    
    # Setup video writer
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(args.output, fourcc, fps, (width, height))
    
    # Storage for profiles
    profiles = {}
    frame_count = 0
    
    print("Starting inference...")
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        if frame_count % 30 == 0:
            print(f"Processing frame {frame_count}")
        
        # YOLO detection
        results = det_model(frame, conf=args.conf)
        detections = []
        
        for r in results:
            if r.boxes is not None:
                for box in r.boxes:
                    cls = int(box.cls[0])
                    label = det_model.names[cls]
                    x1, y1, x2, y2 = map(int, box.xyxy[0])
                    conf = float(box.conf[0])
                    
                    # Process motorcycles
                    if label == "motorcycle" and conf > args.conf:
                        crop = frame[y1:y2, x1:x2]
                        model_label = classify_bike(crop, bike_model, classes, transform, device)
                        bike_color = get_dominant_color(crop)
                        detections.append([x1, y1, x2, y2, model_label, bike_color, "bike"])
                    
                    # Process persons
                    elif label == "person" and conf > args.conf:
                        detections.append([x1, y1, x2, y2, "person", "", "person"])
        
        # Update tracker
        tracks = tracker.update(detections)
        
        # Process tracks and update profiles
        for track in tracks:
            x1, y1, x2, y2, track_id, metadata = track
            
            if metadata.get('type') == 'bike':
                # Initialize or update bike profile
                if track_id not in profiles:
                    profiles[track_id] = {
                        "BikerID": str(track_id),
                        "Model": metadata.get('class', 'unknown'),
                        "BikeColor": metadata.get('color', 'unknown'),
                        "Rider": {"ShirtColor": "unknown", "PantColor": "unknown"},
                        "PillionCount": 0
                    }
                
                # Draw bike detection
                cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                label_text = f"Bike {track_id}: {metadata.get('class', 'unknown')}"
                cv2.putText(frame, label_text, (x1, y1-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
            
            elif metadata.get('type') == 'person':
                # Extract clothing colors
                person_crop = frame[y1:y2, x1:x2]
                shirt_color, pant_color = extract_clothing_colors(person_crop)
                
                # Find nearest bike to assign rider/pillion
                min_distance = float('inf')
                nearest_bike_id = None
                
                person_center = ((x1 + x2) // 2, (y1 + y2) // 2)
                
                for other_track in tracks:
                    ox1, oy1, ox2, oy2, other_id, other_meta = other_track
                    if other_meta.get('type') == 'bike':
                        bike_center = ((ox1 + ox2) // 2, (oy1 + oy2) // 2)
                        distance = ((person_center[0] - bike_center[0])**2 + 
                                  (person_center[1] - bike_center[1])**2)**0.5
                        
                        if distance < min_distance and distance < 100:  # Within 100 pixels
                            min_distance = distance
                            nearest_bike_id = other_id
                
                # Assign to nearest bike
                if nearest_bike_id and nearest_bike_id in profiles:
                    if profiles[nearest_bike_id]["Rider"]["ShirtColor"] == "unknown":
                        # Assign as rider
                        profiles[nearest_bike_id]["Rider"]["ShirtColor"] = shirt_color
                        profiles[nearest_bike_id]["Rider"]["PantColor"] = pant_color
                        person_role = "Rider"
                    else:
                        # Assign as pillion
                        profiles[nearest_bike_id]["PillionCount"] += 1
                        person_role = "Pillion"
                    
                    # Draw person detection
                    cv2.rectangle(frame, (x1, y1), (x2, y2), (255, 0, 0), 2)
                    label_text = f"{person_role} (Bike {nearest_bike_id})"
                    cv2.putText(frame, label_text, (x1, y1-10),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)
        
        # Write frame
        out.write(frame)
        
        # Optional: Display frame (comment out for headless operation)
        # cv2.imshow("Bike & Rider Detection", frame)
        # if cv2.waitKey(1) & 0xFF == ord('q'):
        #     break
    
    # Cleanup
    cap.release()
    out.release()
    cv2.destroyAllWindows()
    
    # Save profiles to JSON
    output_json = "outputs/profiles.json"
    os.makedirs(os.path.dirname(output_json), exist_ok=True)
    
    with open(output_json, "w") as f:
        json.dump(list(profiles.values()), f, indent=4)
    
    print(f"✅ Processing complete!")
    print(f"📹 Output video saved to: {args.output}")
    print(f"📄 Profiles saved to: {output_json}")
    print(f"🏍️ Total bikes detected: {len(profiles)}")

if __name__ == "__main__":
    main()
