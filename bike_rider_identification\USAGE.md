# Bike & Rider Identification System - Usage Guide

## 🚀 Quick Start

### 1. Prerequisites
- Python 3.8+
- All dependencies installed: `pip install -r requirements.txt`
- Trained models in `src/models/` directory

### 2. Basic Usage

#### Option A: Using the Demo Script (Recommended)
```bash
cd src
python demo.py [video_file]
```

#### Option B: Using the Pipeline Directly
```python
from inference_pipeline import BikeRiderInferencePipeline

pipeline = BikeRiderInferencePipeline()
pipeline.run_inference(
    video_path="your_video.mp4",
    output_video_path="outputs/result.mp4", 
    output_json_path="outputs/profiles.json"
)
```

## 📋 System Overview

### Input
- **Video file** (mp4, avi, mov, etc.)

### Processing Pipeline
1. **YOLOv8 Detection**: Detects motorcycles and persons using COCO pretrained model
2. **ResNet Classification**: Classifies bikes into "Splendor" or "Yamaha" categories
3. **Color Detection**: Extracts dominant colors for bikes and clothing
4. **Tracking**: Assigns consistent BikerIDs across frames
5. **Association**: Links riders with bikes and counts pillions

### Output
- **Annotated Video**: `outputs/demo_out.mp4` with bounding boxes and labels
- **JSON Profiles**: `outputs/profiles.json` with detailed information

## 📄 Output Format

### JSON Structure
```json
[
  {
    "BikerID": "1",
    "Model": "Splendor",
    "BikeColor": "black", 
    "Rider": {
      "ShirtColor": "blue",
      "PantColor": "black"
    },
    "PillionCount": 1
  },
  {
    "BikerID": "2",
    "Model": "Yamaha",
    "BikeColor": "red",
    "Rider": {
      "ShirtColor": "white", 
      "PantColor": "blue"
    },
    "PillionCount": 0
  }
]
```

### Field Descriptions
- **BikerID**: Unique identifier for each bike-rider combination
- **Model**: Bike classification ("Splendor" or "Yamaha")
- **BikeColor**: Dominant bike color (red, blue, black, white, other)
- **Rider.ShirtColor**: Rider's shirt color (red, blue, black, white, other)
- **Rider.PantColor**: Rider's pant color (red, blue, black, white, other)
- **PillionCount**: Number of additional passengers on the bike

## 🎯 Model Details

### Bike Classification Logic
- **2-Class Model**: Trained on your datasets
  - Class 0 ("1") → Maps to **"Yamaha"**
  - Class 1 ("Splendor") → Maps to **"Splendor"**
- **4-Class Integration**: All 4-class results (CD-70, CG-125, Prider, yamaha) → **"Yamaha"**

### Color Categories
The system maps all detected colors to 5 categories:
- **red**: Red tones
- **blue**: Blue tones  
- **black**: Dark colors
- **white**: Light/white colors
- **other**: All other colors (green, yellow, gray, etc.)

## 🛠️ Advanced Usage

### Custom Configuration
```python
pipeline = BikeRiderInferencePipeline()

# Modify detection confidence threshold
pipeline.det_model.conf = 0.6  # Default: 0.5

# Run with custom parameters
pipeline.run_inference(
    video_path="input.mp4",
    output_video_path="custom_output.mp4",
    output_json_path="custom_profiles.json"
)
```

### Batch Processing
```python
import os
from inference_pipeline import BikeRiderInferencePipeline

pipeline = BikeRiderInferencePipeline()

video_folder = "input_videos/"
for video_file in os.listdir(video_folder):
    if video_file.endswith(('.mp4', '.avi', '.mov')):
        video_path = os.path.join(video_folder, video_file)
        output_name = os.path.splitext(video_file)[0]
        
        pipeline.run_inference(
            video_path=video_path,
            output_video_path=f"outputs/{output_name}_result.mp4",
            output_json_path=f"outputs/{output_name}_profiles.json"
        )
```

## 🧪 Testing

### Run Component Tests
```bash
cd src
python simple_test.py
```

### Test with Sample Video
```bash
cd src
python demo.py  # Creates sample video if none provided
```

## 📁 File Structure
```
bike_rider_identification/
├── src/
│   ├── inference_pipeline.py    # Main pipeline
│   ├── demo.py                  # Demo script
│   ├── simple_test.py          # Component tests
│   ├── utils.py                # Color detection utilities
│   ├── tracker.py              # Multi-object tracking
│   ├── train_bike2.py          # 2-class model training
│   ├── train_bike4.py          # 4-class model training
│   └── models/
│       ├── resnet_bike2.pth    # Trained 2-class model
│       └── resnet_bike4.pth    # Trained 4-class model
├── outputs/                    # Generated outputs
├── datasets/                   # Training datasets
└── requirements.txt           # Dependencies
```

## ⚠️ Troubleshooting

### Common Issues

1. **"Module not found" errors**
   ```bash
   pip install -r requirements.txt
   ```

2. **YOLO model download issues**
   - Ensure internet connection
   - Models download automatically on first run

3. **Low detection accuracy**
   - Adjust confidence threshold: `pipeline.det_model.conf = 0.3`
   - Ensure good video quality and lighting

4. **Memory issues with large videos**
   - Process shorter video segments
   - Reduce video resolution before processing

### Performance Tips
- Use GPU if available (CUDA)
- Process videos at lower resolution for faster results
- Adjust frame sampling rate for real-time processing

## 📞 Support

For issues or questions:
1. Check the troubleshooting section above
2. Run `python simple_test.py` to verify component functionality
3. Ensure all models are properly trained and saved
