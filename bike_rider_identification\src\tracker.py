import numpy as np
from utils import calculate_iou

class Track:
    """Individual track object"""
    def __init__(self, track_id, bbox, metadata=None):
        self.track_id = track_id
        self.bbox = bbox  # [x1, y1, x2, y2]
        self.metadata = metadata or {}
        self.age = 0
        self.hits = 1
        self.time_since_update = 0
        self.state = 'Tentative'  # Tentative, Confirmed, Deleted
        
    def update(self, bbox, metadata=None):
        """Update track with new detection"""
        self.bbox = bbox
        if metadata:
            self.metadata.update(metadata)
        self.hits += 1
        self.time_since_update = 0
        
        # Confirm track after 3 hits
        if self.hits >= 3:
            self.state = 'Confirmed'
    
    def predict(self):
        """Simple prediction - just keep the same bbox"""
        self.age += 1
        self.time_since_update += 1
        
        # Mark for deletion if not updated for too long
        if self.time_since_update > 30:
            self.state = 'Deleted'
    
    def get_state(self):
        """Get current track state"""
        return [*self.bbox, self.track_id, self.metadata]

class Tracker:
    """Simple multi-object tracker inspired by DeepSORT"""
    
    def __init__(self, max_disappeared=30, max_distance=50):
        self.next_id = 1
        self.tracks = []
        self.max_disappeared = max_disappeared
        self.max_distance = max_distance
        self.iou_threshold = 0.3
    
    def update(self, detections):
        """
        Update tracker with new detections
        
        Args:
            detections: List of [x1, y1, x2, y2, class_name, color, object_type]
        
        Returns:
            List of active tracks: [x1, y1, x2, y2, track_id, metadata]
        """
        # Predict existing tracks
        for track in self.tracks:
            track.predict()
        
        # Remove deleted tracks
        self.tracks = [t for t in self.tracks if t.state != 'Deleted']
        
        if len(detections) == 0:
            return [t.get_state() for t in self.tracks if t.state == 'Confirmed']
        
        # Convert detections to proper format
        det_boxes = []
        det_metadata = []
        
        for det in detections:
            if len(det) >= 4:
                box = det[:4]
                metadata = {
                    'class': det[4] if len(det) > 4 else 'unknown',
                    'color': det[5] if len(det) > 5 else 'unknown',
                    'type': det[6] if len(det) > 6 else 'unknown'
                }
                det_boxes.append(box)
                det_metadata.append(metadata)
        
        if len(det_boxes) == 0:
            return [t.get_state() for t in self.tracks if t.state == 'Confirmed']
        
        # Association using IoU
        matched_tracks, unmatched_dets, unmatched_tracks = self._associate(
            self.tracks, det_boxes, det_metadata
        )
        
        # Update matched tracks
        for track_idx, det_idx in matched_tracks:
            self.tracks[track_idx].update(det_boxes[det_idx], det_metadata[det_idx])
        
        # Create new tracks for unmatched detections
        for det_idx in unmatched_dets:
            new_track = Track(
                self.next_id,
                det_boxes[det_idx],
                det_metadata[det_idx]
            )
            self.tracks.append(new_track)
            self.next_id += 1
        
        # Return confirmed tracks
        return [t.get_state() for t in self.tracks if t.state == 'Confirmed']
    
    def _associate(self, tracks, detections, metadata):
        """Associate tracks with detections using IoU"""
        if len(tracks) == 0:
            return [], list(range(len(detections))), []
        
        if len(detections) == 0:
            return [], [], list(range(len(tracks)))
        
        # Calculate IoU matrix
        iou_matrix = np.zeros((len(tracks), len(detections)))
        
        for t, track in enumerate(tracks):
            for d, det_box in enumerate(detections):
                iou_matrix[t, d] = calculate_iou(track.bbox, det_box)
        
        # Simple greedy matching
        matched_tracks = []
        matched_detections = []
        
        # Find best matches
        while True:
            # Find maximum IoU
            max_iou = np.max(iou_matrix)
            if max_iou < self.iou_threshold:
                break
            
            # Get indices of maximum IoU
            t_idx, d_idx = np.unravel_index(np.argmax(iou_matrix), iou_matrix.shape)
            
            # Add to matches
            matched_tracks.append((t_idx, d_idx))
            matched_detections.append(d_idx)
            
            # Remove matched row and column
            iou_matrix[t_idx, :] = 0
            iou_matrix[:, d_idx] = 0
        
        # Find unmatched detections and tracks
        unmatched_detections = [d for d in range(len(detections)) if d not in matched_detections]
        matched_track_indices = [t for t, d in matched_tracks]
        unmatched_tracks = [t for t in range(len(tracks)) if t not in matched_track_indices]
        
        return matched_tracks, unmatched_detections, unmatched_tracks
    
    def get_active_tracks(self):
        """Get all active (confirmed) tracks"""
        return [t.get_state() for t in self.tracks if t.state == 'Confirmed']
