import torch
import torch.nn as nn
import torch.optim as optim
from torchvision import datasets, transforms, models
import os

# Dataset paths
train_dir = "../datasets/bike_models_2/train"
val_dir = "../datasets/bike_models_2/val"

# Data transforms
transform = transforms.Compose([
    transforms.Resize((224, 224)),
    transforms.RandomHorizontalFlip(),
    transforms.ToTensor(),
    transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
])

# Load datasets
train_data = datasets.ImageFolder(train_dir, transform=transform)
val_data = datasets.ImageFolder(val_dir, transform=transform)

train_loader = torch.utils.data.DataLoader(train_data, batch_size=16, shuffle=True)
val_loader = torch.utils.data.DataLoader(val_data, batch_size=16)

print(f"Classes: {train_data.classes}")
print(f"Training samples: {len(train_data)}")
print(f"Validation samples: {len(val_data)}")

# Model setup
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"Using device: {device}")

model = models.resnet18(pretrained=True)

# Freeze all parameters
for param in model.parameters():
    param.requires_grad = False

# Replace final layer for 2 classes
model.fc = nn.Linear(model.fc.in_features, 2)  # 2 classes: 1, Splendor

model = model.to(device)
criterion = nn.CrossEntropyLoss()
optimizer = optim.Adam(model.fc.parameters(), lr=1e-3)

# Training loop
print("Starting training...")
for epoch in range(5):
    model.train()
    total_loss = 0
    correct = 0
    total = 0
    
    for imgs, labels in train_loader:
        imgs, labels = imgs.to(device), labels.to(device)
        
        optimizer.zero_grad()
        outputs = model(imgs)
        loss = criterion(outputs, labels)
        loss.backward()
        optimizer.step()
        
        total_loss += loss.item()
        _, predicted = torch.max(outputs.data, 1)
        total += labels.size(0)
        correct += (predicted == labels).sum().item()
    
    train_acc = 100 * correct / total
    avg_loss = total_loss / len(train_loader)
    
    # Validation
    model.eval()
    val_correct = 0
    val_total = 0
    with torch.no_grad():
        for imgs, labels in val_loader:
            imgs, labels = imgs.to(device), labels.to(device)
            outputs = model(imgs)
            _, predicted = torch.max(outputs, 1)
            val_total += labels.size(0)
            val_correct += (predicted == labels).sum().item()
    
    val_acc = 100 * val_correct / val_total if val_total > 0 else 0
    
    print(f"Epoch {epoch+1}/5:")
    print(f"  Train Loss: {avg_loss:.4f}, Train Acc: {train_acc:.2f}%")
    print(f"  Val Acc: {val_acc:.2f}%")

# Create models directory if it doesn't exist
os.makedirs("models", exist_ok=True)

# Save model
torch.save(model.state_dict(), "models/resnet_bike2.pth")
print("✅ Saved ResNet 2-class model to models/resnet_bike2.pth")
