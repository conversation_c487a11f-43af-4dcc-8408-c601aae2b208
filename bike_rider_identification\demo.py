#!/usr/bin/env python3
"""
Demo script for Bike & Rider Identification System
Creates a synthetic test video and runs the complete pipeline
"""

import cv2
import numpy as np
import os
import sys
from pathlib import Path

def create_demo_video(output_path="demo_video.mp4", duration=10, fps=20):
    """Create a simple demo video with moving rectangles representing bikes and people"""
    
    width, height = 640, 480
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    total_frames = duration * fps
    
    print(f"🎬 Creating demo video: {output_path}")
    print(f"   Duration: {duration}s, FPS: {fps}, Frames: {total_frames}")
    
    for frame_num in range(total_frames):
        # Create black background
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        
        # Add some background texture
        cv2.rectangle(frame, (0, height-50), (width, height), (50, 50, 50), -1)  # Road
        cv2.line(frame, (0, height-25), (width, height-25), (255, 255, 255), 2)  # Road line
        
        # Simulate moving bikes and riders
        t = frame_num / total_frames
        
        # Bike 1 - moving left to right
        bike1_x = int(50 + t * 400)
        bike1_y = height - 120
        cv2.rectangle(frame, (bike1_x, bike1_y), (bike1_x+80, bike1_y+40), (0, 0, 255), -1)  # Red bike
        cv2.putText(frame, "BIKE", (bike1_x+10, bike1_y+25), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # Rider 1
        rider1_x = bike1_x + 20
        rider1_y = bike1_y - 40
        cv2.rectangle(frame, (rider1_x, rider1_y), (rider1_x+25, rider1_y+35), (255, 0, 0), -1)  # Blue rider
        cv2.putText(frame, "R", (rider1_x+8, rider1_y+20), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
        
        # Bike 2 - moving right to left (if past halfway)
        if t > 0.3:
            bike2_x = int(550 - (t-0.3) * 300)
            bike2_y = height - 150
            cv2.rectangle(frame, (bike2_x, bike2_y), (bike2_x+70, bike2_y+35), (0, 255, 0), -1)  # Green bike
            cv2.putText(frame, "BIKE", (bike2_x+5, bike2_y+20), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 0), 1)
            
            # Rider 2
            rider2_x = bike2_x + 15
            rider2_y = bike2_y - 35
            cv2.rectangle(frame, (rider2_x, rider2_y), (rider2_x+20, rider2_y+30), (0, 255, 255), -1)  # Yellow rider
            cv2.putText(frame, "R", (rider2_x+5, rider2_y+18), cv2.FONT_HERSHEY_SIMPLEX, 0.3, (0, 0, 0), 1)
            
            # Pillion passenger
            if t > 0.5:
                pillion_x = bike2_x + 35
                pillion_y = bike2_y - 30
                cv2.rectangle(frame, (pillion_x, pillion_y), (pillion_x+15, pillion_y+25), (255, 255, 0), -1)  # Cyan pillion
                cv2.putText(frame, "P", (pillion_x+3, pillion_y+15), cv2.FONT_HERSHEY_SIMPLEX, 0.3, (0, 0, 0), 1)
        
        # Add frame info
        cv2.putText(frame, f"Frame: {frame_num+1}/{total_frames}", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
        cv2.putText(frame, "Demo Video - Bike & Rider Detection", (10, height-10), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        out.write(frame)
    
    out.release()
    print(f"✅ Demo video created: {output_path}")
    return output_path

def run_demo():
    """Run the complete demo pipeline"""
    print("🏍️ Bike & Rider Identification System - Demo")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not Path("src/inference.py").exists():
        print("❌ Please run this script from the bike_rider_identification directory")
        print("   Current directory should contain src/ folder")
        sys.exit(1)
    
    # Create demo video
    demo_video = create_demo_video()
    
    # Check if inference script exists
    if not Path("src/inference.py").exists():
        print("❌ inference.py not found in src/ directory")
        sys.exit(1)
    
    print("\n🔄 Running inference pipeline...")
    print("Note: This will use YOLOv8 pretrained model (no bike classification without trained models)")
    
    # Create outputs directory
    os.makedirs("outputs", exist_ok=True)
    
    # Run inference
    import subprocess
    try:
        cmd = [
            sys.executable, "src/inference.py",
            "--input", demo_video,
            "--output", "outputs/demo_output.mp4",
            "--model", "4class",
            "--conf", "0.3"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Inference completed successfully!")
            print(f"📹 Output video: outputs/demo_output.mp4")
            print(f"📄 Profiles JSON: outputs/profiles.json")
            
            # Check if files were created
            if Path("outputs/demo_output.mp4").exists():
                print("✅ Output video file created")
            if Path("outputs/profiles.json").exists():
                print("✅ Profiles JSON file created")
                
        else:
            print("❌ Inference failed:")
            print(result.stderr)
            
    except Exception as e:
        print(f"❌ Error running inference: {e}")
    
    # Cleanup demo video
    if Path(demo_video).exists():
        os.remove(demo_video)
        print(f"🧹 Cleaned up demo video: {demo_video}")
    
    print("\n🎉 Demo completed!")
    print("\n📋 Next steps:")
    print("1. Add real bike images to dataset folders")
    print("2. Train ResNet classifiers with your data")
    print("3. Test with real videos")
    print("\n📖 See README.md for detailed instructions")

if __name__ == "__main__":
    run_demo()
