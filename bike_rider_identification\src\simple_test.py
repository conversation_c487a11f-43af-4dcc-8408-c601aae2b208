#!/usr/bin/env python3
"""
Simple test for core components without YOLO dependency
"""

import os
import json
import numpy as np
import torch
from torchvision import models
from utils import get_dominant_color, map_to_target_colors, extract_clothing_colors

def test_color_detection():
    """Test color detection functions"""
    print("🎨 Testing color detection...")
    
    # Create test images with known colors
    red_img = np.full((100, 100, 3), (0, 0, 255), dtype=np.uint8)  # Red in BGR
    blue_img = np.full((100, 100, 3), (255, 0, 0), dtype=np.uint8)  # Blue in BGR
    white_img = np.full((100, 100, 3), (255, 255, 255), dtype=np.uint8)  # White
    black_img = np.full((100, 100, 3), (0, 0, 0), dtype=np.uint8)  # Black
    
    # Test color detection
    colors = {
        "red": get_dominant_color(red_img),
        "blue": get_dominant_color(blue_img),
        "white": get_dominant_color(white_img),
        "black": get_dominant_color(black_img)
    }
    
    print("  Detected colors:")
    for name, color in colors.items():
        mapped = map_to_target_colors(color)
        print(f"    {name} -> {color} -> {mapped}")
    
    # Test clothing detection
    person_img = np.zeros((200, 100, 3), dtype=np.uint8)
    person_img[0:100, :] = (0, 0, 255)  # Red shirt (top half)
    person_img[100:200, :] = (255, 0, 0)  # Blue pants (bottom half)
    
    clothing = extract_clothing_colors(person_img)
    print(f"  Clothing detection: {clothing}")
    
    return True

def test_bike_model():
    """Test bike classification model loading"""
    print("\n🏍️ Testing bike model...")
    
    if not os.path.exists("models/resnet_bike2.pth"):
        print("  ❌ Model file not found: models/resnet_bike2.pth")
        return False
    
    try:
        # Load model
        model = models.resnet18(pretrained=False)
        model.fc = torch.nn.Linear(model.fc.in_features, 2)
        model.load_state_dict(torch.load("models/resnet_bike2.pth", map_location="cpu"))
        model.eval()
        
        print("  ✅ Model loaded successfully")
        
        # Test inference with dummy data
        dummy_input = torch.randn(1, 3, 224, 224)
        with torch.no_grad():
            output = model(dummy_input)
            pred_class = output.argmax().item()
        
        bike_classes = ["Splendor", "Yamaha"]
        if pred_class == 1:  # Class "Splendor" 
            result = "Splendor"
        else:  # Class "1" -> map to Yamaha
            result = "Yamaha"
        
        print(f"  ✅ Test classification: {result} (class {pred_class})")
        return True
        
    except Exception as e:
        print(f"  ❌ Model test failed: {e}")
        return False

def test_json_format():
    """Test JSON output format"""
    print("\n📄 Testing JSON format...")
    
    # Create sample profiles in expected format
    sample_profiles = [
        {
            "BikerID": "1",
            "Model": "Splendor",
            "BikeColor": "black",
            "Rider": {
                "ShirtColor": "blue",
                "PantColor": "black"
            },
            "PillionCount": 1
        },
        {
            "BikerID": "2", 
            "Model": "Yamaha",
            "BikeColor": "red",
            "Rider": {
                "ShirtColor": "white",
                "PantColor": "blue"
            },
            "PillionCount": 0
        }
    ]
    
    # Create outputs directory
    os.makedirs("outputs", exist_ok=True)
    
    # Save test JSON
    test_json_path = "outputs/test_format.json"
    with open(test_json_path, 'w') as f:
        json.dump(sample_profiles, f, indent=2)
    
    print(f"  ✅ Sample JSON created: {test_json_path}")
    
    # Validate JSON structure
    with open(test_json_path, 'r') as f:
        loaded_profiles = json.load(f)
    
    print(f"  ✅ JSON loaded successfully with {len(loaded_profiles)} profiles")
    
    # Check structure
    required_keys = ["BikerID", "Model", "BikeColor", "Rider", "PillionCount"]
    rider_keys = ["ShirtColor", "PantColor"]
    
    for i, profile in enumerate(loaded_profiles):
        print(f"  Profile {i+1}:")
        for key in required_keys:
            if key in profile:
                if key == "Rider":
                    rider = profile[key]
                    for rkey in rider_keys:
                        if rkey in rider:
                            print(f"    ✅ {key}.{rkey}: {rider[rkey]}")
                        else:
                            print(f"    ❌ Missing {key}.{rkey}")
                else:
                    print(f"    ✅ {key}: {profile[key]}")
            else:
                print(f"    ❌ Missing {key}")
    
    return True

def test_tracker():
    """Test tracking functionality"""
    print("\n🎯 Testing tracker...")
    
    try:
        from tracker import Tracker
        
        tracker = Tracker()
        print("  ✅ Tracker initialized")
        
        # Test with dummy detections
        detections = [
            [100, 100, 200, 200, "Splendor", "black"],  # x1, y1, x2, y2, model, color
            [300, 150, 400, 250, "Yamaha", "red"]
        ]
        
        tracks = tracker.update(detections)
        print(f"  ✅ Tracker updated with {len(tracks)} tracks")
        
        for track in tracks:
            print(f"    Track: {track}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Tracker test failed: {e}")
        return False

def main():
    """Run simple tests"""
    print("🧪 Simple Component Tests")
    print("=" * 40)
    
    tests = [
        ("Color Detection", test_color_detection),
        ("Bike Model", test_bike_model),
        ("JSON Format", test_json_format),
        ("Tracker", test_tracker)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} - PASSED")
            else:
                print(f"❌ {test_name} - FAILED")
        except Exception as e:
            print(f"❌ {test_name} - ERROR: {e}")
            results.append((test_name, False))
        
        print()
    
    # Summary
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print("=" * 40)
    print(f"📊 Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All tests passed! The pipeline components are working correctly.")
        print("\n📋 Next steps:")
        print("  1. Run: python demo.py [video_file]")
        print("  2. Or use: python inference_pipeline.py")
        print("  3. Check outputs/ folder for results")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")

if __name__ == "__main__":
    main()
