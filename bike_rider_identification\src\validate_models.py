import torch
import torch.nn as nn
from torchvision import datasets, transforms, models
import os

def load_model(model_path, num_classes):
    """Load a trained ResNet model"""
    model = models.resnet18(pretrained=False)
    model.fc = nn.Linear(model.fc.in_features, num_classes)
    model.load_state_dict(torch.load(model_path, map_location='cpu'))
    model.eval()
    return model

def test_model(model, test_loader, model_name):
    """Test model accuracy on test set"""
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model = model.to(device)
    
    correct = 0
    total = 0
    class_correct = {}
    class_total = {}
    
    with torch.no_grad():
        for images, labels in test_loader:
            images, labels = images.to(device), labels.to(device)
            outputs = model(images)
            _, predicted = torch.max(outputs, 1)
            
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
            
            # Per-class accuracy
            for i in range(len(labels)):
                label = labels[i].item()
                pred = predicted[i].item()
                
                if label not in class_total:
                    class_total[label] = 0
                    class_correct[label] = 0
                
                class_total[label] += 1
                if label == pred:
                    class_correct[label] += 1
    
    accuracy = 100 * correct / total
    print(f"\n{model_name} Results:")
    print(f"Overall Test Accuracy: {accuracy:.2f}% ({correct}/{total})")
    
    # Print per-class accuracy
    for class_idx in sorted(class_total.keys()):
        class_acc = 100 * class_correct[class_idx] / class_total[class_idx]
        print(f"  Class {class_idx}: {class_acc:.2f}% ({class_correct[class_idx]}/{class_total[class_idx]})")
    
    return accuracy

def main():
    # Data transforms (same as training)
    transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
    ])
    
    print("🔍 Validating trained bike classification models...")
    
    # Test 2-class model
    if os.path.exists("models/resnet_bike2.pth"):
        print("\n" + "="*50)
        print("Testing 2-Class Model (1 vs Splendor)")
        print("="*50)
        
        test_data_2 = datasets.ImageFolder("../datasets/bike_models_2/test", transform=transform)
        test_loader_2 = torch.utils.data.DataLoader(test_data_2, batch_size=16, shuffle=False)
        
        print(f"Classes: {test_data_2.classes}")
        print(f"Test samples: {len(test_data_2)}")
        
        model_2 = load_model("models/resnet_bike2.pth", 2)
        test_model(model_2, test_loader_2, "2-Class Model")
    
    # Test 4-class model
    if os.path.exists("models/resnet_bike4.pth"):
        print("\n" + "="*50)
        print("Testing 4-Class Model (CD-70, CG-125, Prider, yamaha)")
        print("="*50)
        
        test_data_4 = datasets.ImageFolder("../datasets/bike_models_4/test", transform=transform)
        test_loader_4 = torch.utils.data.DataLoader(test_data_4, batch_size=16, shuffle=False)
        
        print(f"Classes: {test_data_4.classes}")
        print(f"Test samples: {len(test_data_4)}")
        
        model_4 = load_model("models/resnet_bike4.pth", 4)
        test_model(model_4, test_loader_4, "4-Class Model")
    
    print("\n✅ Model validation completed!")

if __name__ == "__main__":
    main()
