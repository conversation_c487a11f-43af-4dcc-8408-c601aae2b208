#!/usr/bin/env python3
"""
Demo script for Bike & Rider Identification System
"""

import os
import sys
import cv2
import numpy as np
from inference_pipeline import BikeRiderInferencePipeline

def create_sample_video():
    """Create a simple sample video for testing if no video is provided"""
    print("Creating sample test video...")
    
    # Create a simple test video with colored rectangles
    width, height = 640, 480
    fps = 20
    duration = 5  # seconds
    total_frames = fps * duration
    
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter('sample_video.mp4', fourcc, fps, (width, height))
    
    for frame_num in range(total_frames):
        # Create a frame with moving colored rectangles
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        frame.fill(50)  # Dark gray background
        
        # Add some moving "bikes" and "persons" (just colored rectangles for demo)
        bike_x = int(100 + 50 * np.sin(frame_num * 0.1))
        person_x = bike_x + 30
        
        # Draw bike (green rectangle)
        cv2.rectangle(frame, (bike_x, 200), (bike_x + 80, 280), (0, 255, 0), -1)
        cv2.putText(frame, "BIKE", (bike_x, 190), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # Draw person (blue rectangle)  
        cv2.rectangle(frame, (person_x, 150), (person_x + 30, 250), (255, 0, 0), -1)
        cv2.putText(frame, "PERSON", (person_x, 140), cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 255, 255), 1)
        
        out.write(frame)
    
    out.release()
    print("✅ Sample video created: sample_video.mp4")
    return 'sample_video.mp4'

def main():
    """Main demo function"""
    print("🚀 Bike & Rider Identification System Demo")
    print("=" * 50)
    
    # Check if models exist
    if not os.path.exists("models/resnet_bike2.pth"):
        print("❌ Error: Trained models not found!")
        print("Please run the training scripts first:")
        print("  python train_bike2.py")
        print("  python train_bike4.py")
        return
    
    # Initialize pipeline
    try:
        pipeline = BikeRiderInferencePipeline()
    except Exception as e:
        print(f"❌ Error initializing pipeline: {e}")
        return
    
    # Check for input video
    video_path = None
    
    # Check command line arguments
    if len(sys.argv) > 1:
        video_path = sys.argv[1]
        if not os.path.exists(video_path):
            print(f"❌ Video file not found: {video_path}")
            video_path = None
    
    # Look for common video files in current directory
    if not video_path:
        common_video_files = ['demo_video.mp4', 'test_video.mp4', 'input.mp4', 'video.mp4']
        for filename in common_video_files:
            if os.path.exists(filename):
                video_path = filename
                print(f"📹 Found video file: {video_path}")
                break
    
    # Create sample video if no video found
    if not video_path:
        print("📹 No input video found. Creating sample video for demo...")
        video_path = create_sample_video()
    
    # Run inference
    print(f"\n🔍 Running inference on: {video_path}")
    print("This may take a few minutes depending on video length...")
    
    try:
        pipeline.run_inference(
            video_path=video_path,
            output_video_path="outputs/demo_out.mp4",
            output_json_path="outputs/profiles.json"
        )
        
        print("\n✅ Demo completed successfully!")
        print("\n📁 Output files:")
        print("  📹 Video: outputs/demo_out.mp4")
        print("  📄 JSON:  outputs/profiles.json")
        
        # Display sample of JSON output
        if os.path.exists("outputs/profiles.json"):
            import json
            with open("outputs/profiles.json", 'r') as f:
                profiles = json.load(f)
            
            print(f"\n👥 Detected {len(profiles)} unique bikers:")
            for i, profile in enumerate(profiles[:3]):  # Show first 3
                print(f"  {i+1}. BikerID: {profile['BikerID']}")
                print(f"     Model: {profile['Model']}")
                print(f"     BikeColor: {profile['BikeColor']}")
                print(f"     Rider: {profile['Rider']}")
                print(f"     PillionCount: {profile['PillionCount']}")
                print()
            
            if len(profiles) > 3:
                print(f"     ... and {len(profiles) - 3} more")
        
    except Exception as e:
        print(f"❌ Error during inference: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
