import cv2
import numpy as np
from sklearn.cluster import KMeans

def get_dominant_color(image, k=3):
    """
    Extract dominant color from image using K-means clustering
    
    Args:
        image: OpenCV image (BGR format)
        k: Number of clusters for K-means
    
    Returns:
        str: Color name (red, blue, black, white, other)
    """
    if image is None or image.size == 0:
        return "unknown"
    
    # Convert BGR to RGB
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # Reshape image to be a list of pixels
    pixels = image.reshape(-1, 3)
    
    # Apply K-means clustering
    kmeans = KMeans(n_clusters=k, n_init=10, random_state=42)
    kmeans.fit(pixels)
    
    # Get the most frequent cluster
    counts = np.bincount(kmeans.labels_)
    center = kmeans.cluster_centers_[counts.argmax()]
    
    r, g, b = map(int, center)
    
    # Color classification based on RGB values
    if r > 150 and g < 100 and b < 100:
        return "red"
    elif b > 150 and r < 100 and g < 100:
        return "blue"
    elif r < 80 and g < 80 and b < 80:
        return "black"
    elif r > 200 and g > 200 and b > 200:
        return "white"
    elif g > 150 and r < 100 and b < 100:
        return "green"
    elif r > 150 and g > 150 and b < 100:
        return "yellow"
    elif r > 100 and g > 100 and b > 100:
        return "gray"
    else:
        return "other"

def get_hsv_color(image):
    """
    Alternative color extraction using HSV color space
    
    Args:
        image: OpenCV image (BGR format)
    
    Returns:
        str: Color name
    """
    if image is None or image.size == 0:
        return "unknown"
    
    # Convert to HSV
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    
    # Get average HSV values
    h_mean = np.mean(hsv[:, :, 0])
    s_mean = np.mean(hsv[:, :, 1])
    v_mean = np.mean(hsv[:, :, 2])
    
    # Color classification based on HSV
    if s_mean < 50:  # Low saturation
        if v_mean > 200:
            return "white"
        elif v_mean < 50:
            return "black"
        else:
            return "gray"
    else:  # High saturation
        if 0 <= h_mean < 10 or 170 <= h_mean <= 180:
            return "red"
        elif 10 <= h_mean < 25:
            return "orange"
        elif 25 <= h_mean < 35:
            return "yellow"
        elif 35 <= h_mean < 85:
            return "green"
        elif 85 <= h_mean < 130:
            return "blue"
        elif 130 <= h_mean < 170:
            return "purple"
        else:
            return "other"

def map_to_target_colors(color_name):
    """
    Map detected color to target categories: red, blue, black, white, other

    Args:
        color_name: Detected color name

    Returns:
        str: Mapped color (red, blue, black, white, other)
    """
    color_mapping = {
        "red": "red",
        "blue": "blue",
        "black": "black",
        "white": "white",
        "gray": "other",
        "grey": "other",
        "green": "other",
        "yellow": "other",
        "orange": "other",
        "purple": "other",
        "unknown": "other"
    }
    return color_mapping.get(color_name.lower(), "other")

def extract_clothing_colors(person_crop):
    """
    Extract shirt and pant colors from person crop

    Args:
        person_crop: OpenCV image of person

    Returns:
        dict: {"ShirtColor": color, "PantColor": color}
    """
    if person_crop is None or person_crop.size == 0:
        return {"ShirtColor": "other", "PantColor": "other"}

    height = person_crop.shape[0]

    # Split into upper and lower body (more refined regions)
    upper_body = person_crop[int(height*0.1):int(height*0.5), :]  # Upper torso for shirt
    lower_body = person_crop[int(height*0.6):int(height*0.9), :]  # Lower body for pants

    # Get dominant colors
    shirt_color_raw = get_dominant_color(upper_body)
    pant_color_raw = get_dominant_color(lower_body)

    # Map to target categories
    shirt_color = map_to_target_colors(shirt_color_raw)
    pant_color = map_to_target_colors(pant_color_raw)

    return {
        "ShirtColor": shirt_color,
        "PantColor": pant_color
    }

def calculate_iou(box1, box2):
    """
    Calculate Intersection over Union (IoU) of two bounding boxes
    
    Args:
        box1, box2: [x1, y1, x2, y2] format
    
    Returns:
        float: IoU value
    """
    x1 = max(box1[0], box2[0])
    y1 = max(box1[1], box2[1])
    x2 = min(box1[2], box2[2])
    y2 = min(box1[3], box2[3])
    
    if x2 <= x1 or y2 <= y1:
        return 0.0
    
    intersection = (x2 - x1) * (y2 - y1)
    area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
    area2 = (box2[2] - box2[0]) * (box2[3] - box2[1])
    union = area1 + area2 - intersection
    
    return intersection / union if union > 0 else 0.0
