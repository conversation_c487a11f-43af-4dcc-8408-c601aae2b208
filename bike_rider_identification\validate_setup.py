#!/usr/bin/env python3
"""
Validation script to check if all components are properly set up
"""

import sys
import importlib
from pathlib import Path

def check_imports():
    """Check if all required packages can be imported"""
    required_packages = [
        'torch',
        'torchvision', 
        'cv2',
        'numpy',
        'sklearn',
        'PIL',
        'ultralytics'
    ]
    
    print("🔍 Checking package imports...")
    failed_imports = []
    
    for package in required_packages:
        try:
            importlib.import_module(package)
            print(f"  ✅ {package}")
        except ImportError as e:
            print(f"  ❌ {package}: {e}")
            failed_imports.append(package)
    
    return len(failed_imports) == 0, failed_imports

def check_file_structure():
    """Check if all required files exist"""
    required_files = [
        'requirements.txt',
        'README.md',
        'src/inference.py',
        'src/train_bike2.py', 
        'src/train_bike4.py',
        'src/tracker.py',
        'src/utils.py'
    ]
    
    required_dirs = [
        'datasets/bike_models_2/train/1',
        'datasets/bike_models_2/train/Splendor',
        'datasets/bike_models_2/val/1',
        'datasets/bike_models_2/val/Splendor',
        'datasets/bike_models_4/train/CD-70',
        'datasets/bike_models_4/train/CG-125',
        'datasets/bike_models_4/train/Prider',
        'datasets/bike_models_4/train/yamaha',
        'datasets/bike_models_4/val/CD-70',
        'datasets/bike_models_4/val/CG-125',
        'datasets/bike_models_4/val/Prider',
        'datasets/bike_models_4/val/yamaha',
        'models',
        'outputs'
    ]
    
    print("\n🔍 Checking file structure...")
    missing_files = []
    missing_dirs = []
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path}")
            missing_files.append(file_path)
    
    for dir_path in required_dirs:
        if Path(dir_path).exists():
            print(f"  ✅ {dir_path}/")
        else:
            print(f"  ❌ {dir_path}/")
            missing_dirs.append(dir_path)
    
    return len(missing_files) == 0 and len(missing_dirs) == 0, missing_files, missing_dirs

def check_python_modules():
    """Check if custom modules can be imported"""
    print("\n🔍 Checking custom modules...")
    
    # Add src to path
    sys.path.insert(0, str(Path('src').absolute()))
    
    modules = ['utils', 'tracker']
    failed_modules = []
    
    for module in modules:
        try:
            importlib.import_module(module)
            print(f"  ✅ {module}")
        except ImportError as e:
            print(f"  ❌ {module}: {e}")
            failed_modules.append(module)
    
    return len(failed_modules) == 0, failed_modules

def check_gpu_availability():
    """Check GPU availability for PyTorch"""
    print("\n🔍 Checking GPU availability...")
    
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            print(f"  ✅ GPU available: {gpu_name} (Count: {gpu_count})")
            return True
        else:
            print("  ⚠️ No GPU available - will use CPU (slower)")
            return False
    except Exception as e:
        print(f"  ❌ Error checking GPU: {e}")
        return False

def main():
    """Main validation function"""
    print("🏍️ Bike & Rider Identification System - Setup Validation")
    print("=" * 60)
    
    all_good = True
    
    # Check imports
    imports_ok, failed_imports = check_imports()
    if not imports_ok:
        print(f"\n❌ Missing packages: {', '.join(failed_imports)}")
        print("   Run: pip install -r requirements.txt")
        all_good = False
    
    # Check file structure
    structure_ok, missing_files, missing_dirs = check_file_structure()
    if not structure_ok:
        print(f"\n❌ Missing files: {missing_files}")
        print(f"❌ Missing directories: {missing_dirs}")
        all_good = False
    
    # Check custom modules
    modules_ok, failed_modules = check_python_modules()
    if not modules_ok:
        print(f"\n❌ Failed to import custom modules: {failed_modules}")
        all_good = False
    
    # Check GPU
    gpu_available = check_gpu_availability()
    
    # Final summary
    print("\n" + "=" * 60)
    if all_good:
        print("🎉 All checks passed! System is ready to use.")
        print("\n📋 Next steps:")
        print("1. Add bike images to dataset folders")
        print("2. Train models: python src/train_bike2.py")
        print("3. Run inference: python src/inference.py --input video.mp4")
        print("4. Or run demo: python demo.py")
        
        if gpu_available:
            print("\n⚡ GPU acceleration available for faster processing")
        else:
            print("\n💡 Consider using GPU for faster training and inference")
            
    else:
        print("❌ Some issues found. Please fix them before proceeding.")
        print("\n🔧 Common fixes:")
        print("- Run: pip install -r requirements.txt")
        print("- Run: python setup.py")
        print("- Check file permissions")
    
    return all_good

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
