#!/usr/bin/env python3
"""
Test script for the Bike & Rider Identification Pipeline
"""

import os
import json
import cv2
import numpy as np
from inference_pipeline import BikeRiderInferencePipeline

def create_test_video():
    """Create a simple test video with basic shapes"""
    print("Creating test video...")
    
    width, height = 640, 480
    fps = 10
    duration = 3  # seconds
    total_frames = fps * duration
    
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter('test_input.mp4', fourcc, fps, (width, height))
    
    for frame_num in range(total_frames):
        # Create frame
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        frame.fill(100)  # Gray background
        
        # Add some basic shapes that might be detected
        # Moving "bike" (green rectangle)
        bike_x = int(200 + 100 * np.sin(frame_num * 0.2))
        cv2.rectangle(frame, (bike_x, 250), (bike_x + 120, 350), (0, 200, 0), -1)
        
        # Moving "person" (blue rectangle)
        person_x = bike_x + 60
        cv2.rectangle(frame, (person_x, 180), (person_x + 40, 280), (200, 100, 0), -1)
        
        # Add some color variation
        cv2.rectangle(frame, (50, 50), (150, 150), (0, 0, 255), -1)  # Red square
        cv2.rectangle(frame, (450, 50), (550, 150), (255, 255, 255), -1)  # White square
        
        out.write(frame)
    
    out.release()
    print("✅ Test video created: test_input.mp4")
    return 'test_input.mp4'

def test_color_detection():
    """Test color detection functions"""
    print("\n🎨 Testing color detection...")
    
    from utils import get_dominant_color, map_to_target_colors, extract_clothing_colors
    
    # Create test images with known colors
    red_img = np.full((100, 100, 3), (0, 0, 255), dtype=np.uint8)  # Red in BGR
    blue_img = np.full((100, 100, 3), (255, 0, 0), dtype=np.uint8)  # Blue in BGR
    white_img = np.full((100, 100, 3), (255, 255, 255), dtype=np.uint8)  # White
    black_img = np.full((100, 100, 3), (0, 0, 0), dtype=np.uint8)  # Black
    
    # Test color detection
    red_color = get_dominant_color(red_img)
    blue_color = get_dominant_color(blue_img)
    white_color = get_dominant_color(white_img)
    black_color = get_dominant_color(black_img)
    
    print(f"  Red image detected as: {red_color}")
    print(f"  Blue image detected as: {blue_color}")
    print(f"  White image detected as: {white_color}")
    print(f"  Black image detected as: {black_color}")
    
    # Test mapping
    mapped_red = map_to_target_colors(red_color)
    mapped_blue = map_to_target_colors(blue_color)
    mapped_white = map_to_target_colors(white_color)
    mapped_black = map_to_target_colors(black_color)
    
    print(f"  Mapped colors: {mapped_red}, {mapped_blue}, {mapped_white}, {mapped_black}")
    
    # Test clothing detection
    person_img = np.zeros((200, 100, 3), dtype=np.uint8)
    person_img[0:100, :] = (0, 0, 255)  # Red shirt (top half)
    person_img[100:200, :] = (255, 0, 0)  # Blue pants (bottom half)
    
    clothing = extract_clothing_colors(person_img)
    print(f"  Clothing detection: {clothing}")
    
    print("✅ Color detection tests completed")

def test_bike_classification():
    """Test bike classification"""
    print("\n🏍️ Testing bike classification...")
    
    # Create pipeline
    pipeline = BikeRiderInferencePipeline()
    
    # Create test bike images
    test_bike = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
    
    # Test classification
    result = pipeline.classify_bike(test_bike)
    print(f"  Test bike classified as: {result}")
    
    # Test with empty image
    empty_result = pipeline.classify_bike(None)
    print(f"  Empty image classified as: {empty_result}")
    
    print("✅ Bike classification tests completed")

def test_full_pipeline():
    """Test the complete pipeline"""
    print("\n🚀 Testing complete pipeline...")
    
    # Create test video
    video_path = create_test_video()
    
    # Initialize pipeline
    pipeline = BikeRiderInferencePipeline()
    
    # Run inference
    output_video = "outputs/test_output.mp4"
    output_json = "outputs/test_profiles.json"
    
    try:
        pipeline.run_inference(
            video_path=video_path,
            output_video_path=output_video,
            output_json_path=output_json
        )
        
        # Check outputs
        if os.path.exists(output_video):
            print(f"✅ Output video created: {output_video}")
        else:
            print(f"❌ Output video not created")
        
        if os.path.exists(output_json):
            print(f"✅ Output JSON created: {output_json}")
            
            # Validate JSON format
            with open(output_json, 'r') as f:
                profiles = json.load(f)
            
            print(f"  📊 Profiles found: {len(profiles)}")
            
            # Check JSON structure
            if profiles:
                sample_profile = profiles[0]
                required_keys = ["BikerID", "Model", "BikeColor", "Rider", "PillionCount"]
                
                for key in required_keys:
                    if key in sample_profile:
                        print(f"  ✅ {key}: {sample_profile[key]}")
                    else:
                        print(f"  ❌ Missing key: {key}")
                
                # Check Rider structure
                if "Rider" in sample_profile and isinstance(sample_profile["Rider"], dict):
                    rider = sample_profile["Rider"]
                    rider_keys = ["ShirtColor", "PantColor"]
                    for key in rider_keys:
                        if key in rider:
                            print(f"  ✅ Rider.{key}: {rider[key]}")
                        else:
                            print(f"  ❌ Missing Rider.{key}")
        else:
            print(f"❌ Output JSON not created")
        
        # Cleanup test files
        if os.path.exists(video_path):
            os.remove(video_path)
            print(f"🧹 Cleaned up test video: {video_path}")
        
        print("✅ Full pipeline test completed")
        
    except Exception as e:
        print(f"❌ Pipeline test failed: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Run all tests"""
    print("🧪 Bike & Rider Identification System - Test Suite")
    print("=" * 60)
    
    # Check if models exist
    if not os.path.exists("models/resnet_bike2.pth"):
        print("❌ Error: Trained models not found!")
        print("Please run the training scripts first.")
        return
    
    try:
        # Run tests
        test_color_detection()
        test_bike_classification()
        test_full_pipeline()
        
        print("\n🎉 All tests completed!")
        
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
