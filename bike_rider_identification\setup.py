#!/usr/bin/env python3
"""
Setup script for Bike & Rider Identification System
"""

import os
import subprocess
import sys
from pathlib import Path

def run_command(command, description):
    """Run a shell command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        print(f"Error output: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 7):
        print("❌ Python 3.7 or higher is required")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    return True

def install_requirements():
    """Install required packages"""
    return run_command("pip install -r requirements.txt", "Installing requirements")

def download_yolo_model():
    """Download YOLOv8 model if not present"""
    model_path = Path("models/yolov8n.pt")
    if model_path.exists():
        print("✅ YOLOv8 model already exists")
        return True
    
    # Create models directory
    os.makedirs("models", exist_ok=True)
    
    # The model will be auto-downloaded by ultralytics on first use
    print("✅ YOLOv8 model will be downloaded automatically on first run")
    return True

def create_sample_data():
    """Create sample dataset structure"""
    print("🔄 Creating sample dataset structure...")
    
    # Create placeholder files to show structure
    sample_dirs = [
        "datasets/bike_models_2/train/1",
        "datasets/bike_models_2/train/Splendor", 
        "datasets/bike_models_2/val/1",
        "datasets/bike_models_2/val/Splendor",
        "datasets/bike_models_4/train/CD-70",
        "datasets/bike_models_4/train/CG-125",
        "datasets/bike_models_4/train/Prider",
        "datasets/bike_models_4/train/yamaha",
        "datasets/bike_models_4/val/CD-70",
        "datasets/bike_models_4/val/CG-125", 
        "datasets/bike_models_4/val/Prider",
        "datasets/bike_models_4/val/yamaha"
    ]
    
    for dir_path in sample_dirs:
        os.makedirs(dir_path, exist_ok=True)
        # Create a README in each directory
        readme_path = Path(dir_path) / "README.txt"
        with open(readme_path, "w") as f:
            f.write(f"Place {Path(dir_path).name} bike images here\n")
            f.write("Supported formats: .jpg, .jpeg, .png\n")
    
    print("✅ Sample dataset structure created")
    return True

def main():
    """Main setup function"""
    print("🏍️ Bike & Rider Identification System Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install requirements
    if not install_requirements():
        print("⚠️ Failed to install requirements. Please install manually:")
        print("pip install -r requirements.txt")
        sys.exit(1)
    
    # Download YOLO model
    if not download_yolo_model():
        print("⚠️ Failed to setup YOLO model")
        sys.exit(1)
    
    # Create sample data structure
    if not create_sample_data():
        print("⚠️ Failed to create sample dataset structure")
        sys.exit(1)
    
    print("\n🎉 Setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Add your bike images to the dataset folders")
    print("2. Train the ResNet classifiers:")
    print("   cd src && python train_bike2.py")
    print("   cd src && python train_bike4.py")
    print("3. Run inference:")
    print("   cd src && python inference.py --input your_video.mp4")
    print("\n📖 See README.md for detailed instructions")

if __name__ == "__main__":
    main()
