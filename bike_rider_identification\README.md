# 🏍️ Bike & Rider Identification System

An overnight MVP for detecting and identifying motorcycles and riders using YOLOv8 for detection and ResNet for bike model classification.

## 🚀 Features

- **Object Detection**: YOLOv8 pretrained on COCO dataset (no retraining required)
- **Bike Classification**: ResNet18 classifiers for bike models (2-class and 4-class options)
- **Color Extraction**: Dominant color detection for bikes and rider clothing
- **Multi-Object Tracking**: DeepSORT-inspired tracking for consistent BikerID assignment
- **Rider Analysis**: Automatic detection of riders and pillion passengers
- **JSON Output**: Structured data export with bike and rider profiles

## 📂 Project Structure

```
bike_rider_identification/
├── datasets/                  
│   ├── bike_models_2/          # 2-class dataset (1, Splendor)
│   │   ├── train/1/
│   │   ├── train/Splendor/
│   │   ├── val/1/
│   │   ├── val/Splendor/
│   │   └── test/...
│   │
│   ├── bike_models_4/          # 4-class dataset
│   │   ├── train/CD-70/
│   │   ├── train/CG-125/
│   │   ├── train/Prider/
│   │   ├── train/yamaha/
│   │   └── val/...
│
├── models/                    
│   ├── yolov8n.pt             # YOLOv8 nano (auto-downloaded)
│   ├── resnet_bike2.pth       # 2-class ResNet (after training)
│   └── resnet_bike4.pth       # 4-class ResNet (after training)
│
├── src/                       
│   ├── train_bike2.py         # ResNet training (2 classes)
│   ├── train_bike4.py         # ResNet training (4 classes)
│   ├── inference.py           # Main inference pipeline
│   ├── tracker.py             # Multi-object tracking
│   └── utils.py               # Color extraction utilities
│
├── outputs/                   # Generated outputs
│   ├── demo_out.mp4          # Annotated video
│   └── profiles.json         # Bike and rider profiles
│
├── requirements.txt           
└── README.md
```

## 🛠️ Installation

1. **Clone and navigate to project:**
```bash
cd bike_rider_identification
```

2. **Install dependencies:**
```bash
pip install -r requirements.txt
```

3. **Prepare your datasets:**
   - Add bike images to appropriate folders in `datasets/bike_models_2/` or `datasets/bike_models_4/`
   - Organize images by class in train/val/test splits

## 🎯 Usage

### 1. Train Bike Classifiers

**For 2-class model (1, Splendor):**
```bash
cd src
python train_bike2.py
```

**For 4-class model (CD-70, CG-125, Prider, yamaha):**
```bash
cd src
python train_bike4.py
```

### 2. Run Inference

**Basic usage:**
```bash
cd src
python inference.py --input demo_video.mp4 --output ../outputs/demo_out.mp4
```

**With options:**
```bash
python inference.py \
    --input your_video.mp4 \
    --output ../outputs/result.mp4 \
    --model 4class \
    --conf 0.6
```

**Parameters:**
- `--input`: Input video path
- `--output`: Output video path  
- `--model`: Choose `2class` or `4class` bike classifier
- `--conf`: YOLO confidence threshold (default: 0.5)

## 📊 Output Format

### JSON Profile Structure
```json
[
  {
    "BikerID": "1",
    "Model": "yamaha", 
    "BikeColor": "red",
    "Rider": {
      "ShirtColor": "blue",
      "PantColor": "black"
    },
    "PillionCount": 1
  }
]
```

### Video Output
- Annotated video with bounding boxes
- Bike model labels and track IDs
- Rider/pillion identification
- Real-time color analysis

## 🔧 Technical Details

### Detection Pipeline
1. **YOLOv8 Detection**: Detects motorcycles and persons using COCO pretrained weights
2. **Bike Classification**: ResNet18 classifies detected motorcycles into specific models
3. **Color Extraction**: K-means clustering for dominant color detection
4. **Tracking**: IoU-based association for consistent object tracking
5. **Rider Assignment**: Spatial proximity analysis to assign riders to bikes

### Supported Bike Models
- **2-class**: 1, Splendor
- **4-class**: CD-70, CG-125, Prider, yamaha

### Color Detection
- **Bike Colors**: red, blue, black, white, green, yellow, gray, other
- **Clothing Colors**: Separate detection for shirt and pants

## 🎨 Customization

### Adding New Bike Models
1. Create new dataset folders in `datasets/`
2. Modify class lists in training scripts
3. Update `inference.py` with new class names
4. Retrain ResNet classifier

### Adjusting Color Detection
- Modify RGB/HSV thresholds in `utils.py`
- Add new color categories
- Tune K-means clustering parameters

### Tracking Parameters
- Adjust IoU threshold in `tracker.py`
- Modify track lifecycle parameters
- Change association distance metrics

## 🚀 Quick Start Example

```bash
# 1. Install dependencies
pip install -r requirements.txt

# 2. Train 4-class model (if you have data)
cd src
python train_bike4.py

# 3. Run inference on demo video
python inference.py --input demo_video.mp4 --model 4class

# 4. Check outputs
ls ../outputs/
# demo_out.mp4  profiles.json
```

## 📈 Performance Notes

- **Real-time capable** on GPU (RTX 3060+)
- **CPU inference** supported but slower
- **Memory usage**: ~2GB GPU memory for inference
- **Accuracy**: Depends on training data quality

## 🔍 Troubleshooting

**Model not found errors:**
- Ensure you've trained the ResNet models first
- Check model paths in `inference.py`

**Poor bike classification:**
- Add more training data
- Increase training epochs
- Use data augmentation

**Tracking issues:**
- Adjust IoU threshold
- Modify tracking parameters
- Check video quality and frame rate

## 📝 License

This project is for educational and research purposes. Please ensure compliance with YOLO and PyTorch licenses.
